import { BattleStatisticsModel } from '../model/battleStatisticsModel.js';

class BattleStatisticsRepository {
    /**
     * Retrieves battle statistics for a user
     * @param {Object} filter - The filter to be used
     * @returns {Promise<Object>} The battle statistics for the user.
     */
    static async getBattleStatistics(filter) {
        try {
            const result = await BattleStatisticsModel.findOne(
                filter
            )

            return result
        } catch (error) {
            throw new Error(`Error getting battle statistics: ${error.message}`)
        }
    }

    /**
     * Registers a new battle in the database.
     * @param {Object} battle - The battle to be registered
     * @returns {Promise<Object>} - The new battle statistics for the user.
     */
    static async registerBattle(battle) {
        try {
            const incObject = {
                wins: 0, ties: 0, losses: 0,
                xpBalance: 0
            }

            incObject.xpBalance = battle.xpDelta

            switch (battle.result) {
                case 'win': 
                    incObject.wins = 1; break
                case 'tie': 
                    incObject.ties = 1; break
                case 'loss': 
                    incObject.losses = 1; break
            }

            const { userId } = battle

            const result = await BattleStatisticsModel.findOneAndUpdate(
                { userId },
                {
                    $inc: incObject,
                    $setOnInsert: { userId }
                },
                { new: true, upsert: true }
            )

            return result
        } catch (error) {
            throw new Error(`Error registering battle: ${error.message}`)
        }
    }

    /**
     * Registers new battles in the database.
     * @param {Array<Object>} battles - The battles to be registered
     * @returns {Promise<Object>} - The new battle statistics for the user(s).
     */
    static async registerBattles(battles) {
        try {
            const incObjects = {}
            
            // Iterate over battle objects (not indices)
            for (const battle of battles) {
                if (!incObjects[battle.userId]) {
                    incObjects[battle.userId] = {
                        wins: 0, ties: 0, losses: 0,
                        xpBalance: 0
                    }
                }

                const incObject = incObjects[battle.userId]
                incObject.xpBalance += battle.xpDelta

                switch (battle.result) {
                    case 'win': 
                        incObject.wins += 1; break
                    case 'tie': 
                        incObject.ties += 1; break
                    case 'loss': 
                        incObject.losses += 1; break
                }
            }

            const userIds = Object.keys(incObjects)

            // Check which users don't have battle statistics
            const existingUsers = await BattleStatisticsModel.find({
                userId: { $in: userIds }
            }).select('userId')

            const existingUserIds = existingUsers.map(user => user.userId)
            const missingUserIds = userIds.filter(userId => !existingUserIds.includes(userId))

            // Create battle statistics for users that don't exist
            if (missingUserIds.length > 0) {
                const newUserStats = missingUserIds.map(userId => ({
                    userId,
                    wins: 0,
                    ties: 0,
                    losses: 0,
                    xpBalance: 0
                }))

                await BattleStatisticsModel.insertMany(newUserStats)
            }

            // Now perform bulk update for all users
            await BattleStatisticsModel.bulkWrite(
                Object.entries(incObjects).map(([userId, incObject]) => ({
                    updateOne: {
                        filter: { userId },
                        update: {
                            $inc: incObject
                        }
                    }
                }))
            )

            // Return the updated battle statistics for all affected users
            const result = await BattleStatisticsModel.find({
                userId: {
                    $in: userIds
                }
            })

            return result
        } catch (error) {
            throw new Error(`Error registering battles: ${error.message}`)
        }
    }
}

export default BattleStatisticsRepository
