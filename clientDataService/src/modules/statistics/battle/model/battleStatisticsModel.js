import mongoose from 'mongoose';

const { Schema } = mongoose;

// Define the schema
const battleStatisticsSchema = new Schema({
    userId: {
        type: String,
        required: true,
        index: true
    },
    wins: {
        type: Number,
        default: 0
    },
    ties: {
        type: Number,
        default: 0
    },
    losses: {
        type: Number,
        default: 0
    },
    xpBalance: {
        type: Number,
        default: 0
    }
}, { 
    _id: false, 
    timestamps: true 
});

// Create the model using the schema
const BattleStatisticsModel = mongoose.model('BattleStatistics', battleStatisticsSchema);

export { BattleStatisticsModel };
