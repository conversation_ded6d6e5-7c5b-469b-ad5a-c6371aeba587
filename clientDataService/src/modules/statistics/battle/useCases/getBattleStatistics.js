import {
    CompositeValidator,
    ExpectedFieldsValidator,
    TypeValidator
} from '../../../../utils/validators/index.js'
import BattleStatisticsRepository from '../repositories/battleStatisticsRepository.js'

const validator = new CompositeValidator()
    .setNext(
        new ExpectedFieldsValidator({
            expectedFields: [
                'userId',
            ]
        })
    ).setNext(
        new TypeValidator({
            userId: 'string',
        })
    )

export async function getBattleStatistics(data) {
    try {
        validator.validate(data)

        const filter = {
            userId: data.userId
        }

        const result = await BattleStatisticsRepository.getBattleStatistics(
            filter
        )

        return result
    } catch (error) {
        throw new Error(`Error getting battle statistics: ${error.message}`)
    }
}

export default getBattleStatistics
