import BattleStatisticsRepository from '../repositories/battleStatisticsRepository.js'
import battleValidator from '../utils/battleValidator.js'

export async function registerBattles(datas) {
    try {
        if (!Array.isArray(datas)) throw new Error(`Invalid datas: ${datas}`)

        datas.forEach(data => battleValidator.validate(data))

        const battles = datas.map(data => ({
            userId: data.userId,
            result: data.result,
            xpDelta: data.xpDelta
        }))

        const result = await BattleStatisticsRepository.registerBattles(
            battles
        )

        return result
    } catch (error) {
        throw new Error(`Error registering battles: ${error.message}`)
    }
}

export default registerBattles