import BattleStatisticsRepository from '../repositories/battleStatisticsRepository.js'
import battleValidator from '../utils/battleValidator.js'

export async function registerBattle(data) {
    try {
        battleValidator.validate(data)

        battle = {
            userId: data.userId,
            result: data.result,
            xpDelta: data.xpDelta
        }

        const result = await BattleStatisticsRepository.registerBattle(
            battle
        )

        return result
    } catch (error) {
        throw new Error(`Error registering battle: ${error.message}`)
    }
}

export default registerBattle
