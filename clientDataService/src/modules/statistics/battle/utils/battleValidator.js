import {
    CompositeValidator,
    EnumValidator,
    ExpectedFieldsValidator,
    TypeValidator
} from '../../../../utils/validators/index.js'

const battleValidator = new CompositeValidator()
    .setNext(
        new ExpectedFieldsValidator({
            expectedFields: [
                'userId',
                'result',
                'xpDelta'
            ]
        })
    ).setNext(
        new TypeValidator({
            userId: 'string',
            result: 'string',
            xpDelta: 'number'
        })
    ).setNext(
        new EnumValidator({
            allowedValues: {
                result: [
                    'win',
                    'tie',
                    'loss'
                ]
            }
        })
    )

export default battleValidator
