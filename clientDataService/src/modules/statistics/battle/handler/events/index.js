import connectToDB from "../../../../../infra/libs/mongodb/connect.js"
import EventRouter from './eventRouter.js';
import {apiResponse} from "../../../../../utils/apiResponse.js"
import disconnectFromDB from "../../../../../infra/libs/mongodb/disconnect.js"

export async function handler(event) {
    try {
        await connectToDB()
        const records = event.Records || []
        const results = await Promise.all(records.map(async (record) => {
            try {
                return {
                    id: record.messageId,
                    status: 'success',
                    result: await EventRouter.route(JSON.parse(record.body))
                }
            } catch (error) {
                return {
                    id: record.messageId,
                    status: 'error',
                    error: error.message
                }
            }
        }))
        await disconnectFromDB()
        return apiResponse(200, { body: results })
    } catch (error) {
        return apiResponse(500, { body: error.message })
    }
}

export default handler
