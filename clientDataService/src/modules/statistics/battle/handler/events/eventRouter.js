import {registerBattle, registerBattles} from "../../useCases/index.js"

// EventRouter for handling different SNS/SQS events
export default class EventRouter {
    static async route(body) {
        const { eventType, data } = body;
        switch (eventType) {
            case "registerBattle":
                return await registerBattle(data);
            case "registerBattles":
                return await registerBattles(data);
            default:
                throw new Error(`Unknown event type: ${eventType}`);
        }
    }
}
