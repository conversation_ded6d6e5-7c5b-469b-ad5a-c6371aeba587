import connectToDB from "../../../../infra/libs/mongodb/connect.js"
import disconnectFromDB from "../../../../infra/libs/mongodb/disconnect.js"
import { apiResponse } from "../../../../utils/apiResponse.js"
import { getBattleStatistics } from "../useCases/index.js"

export async function handler(event) {
    try {
        await connectToDB()
        const { id: userId } = event.requestContext.authorizer || {};
        const result = await getBattleStatistics({ userId })
        await disconnectFromDB()
        return apiResponse(200, { body: result })
    } catch (error) {
        return apiResponse(500, { body: error.message })
    }
}

export default handler
