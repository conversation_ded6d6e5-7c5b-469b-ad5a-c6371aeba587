"use client"
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useSearchParams } from 'next/navigation'
import ChatPollingClient from '@/services/COMMUNICATION/api/ChatPollingClient'
import CommunicationApi from '@/services/COMMUNICATION/api/CommunicationApi'
import type { Channel } from '@/services/COMMUNICATION/interfaces/channel'
import type { Message } from '@/services/COMMUNICATION/interfaces/message'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { toast } from '@/hooks/use-toast'
import { Image as ImageIcon, SmilePlus, Send } from 'lucide-react'
import dynamic from 'next/dynamic'
import { sanitizeMessage } from './_utils/messageSanitizer'
import { PresenceList } from './PresenceIndicator'
import WhatsAppLoader from './WhatsAppLoader'
import { useAuth } from '@/context/AuthContext'
import './chat-styles.css'

// @ts-ignore - ReactMarkdown type issues with dynamic import
const ReactMarkdown = dynamic(() => import('react-markdown'), { ssr: false })

interface CommunicationChatProps {
  teacherView?: boolean
}

export default function CommunicationChat({ teacherView = false }: CommunicationChatProps) {
  const searchParams = useSearchParams()
  const targetChannelId = searchParams?.get('channel')
  const { authData } = useAuth()

  const [channels, setChannels] = useState<Channel[]>([])
  const [selectedChannel, setSelectedChannel] = useState<Channel | null>(null)
  const [messages, setMessages] = useState<Record<string, Message[]>>({})
  const [input, setInput] = useState('')
  const [uploading, setUploading] = useState(false)
  const [unreadCount, setUnreadCount] = useState(0)
  const [perChannelUnread, setPerChannelUnread] = useState<Record<string, number>>({})
  const [isLoadingChannels, setIsLoadingChannels] = useState(true)
  const [isLoadingMessages, setIsLoadingMessages] = useState(false)
  const [sending, setSending] = useState(false)
  const [showWhatsAppLoader, setShowWhatsAppLoader] = useState(true)
  const containerRef = useRef<HTMLDivElement | null>(null)

  const polling = useMemo(() => new ChatPollingClient({ foregroundMs: 4000, backgroundMs: 20000, maxBackoffMs: 60000 }, {
    onMessages: (channelId, newMsgs) => {
      // Validate channelId before processing messages
      if (!channelId || channelId === 'undefined') {
        console.warn('[CommunicationChat] Invalid channelId in onMessages:', channelId);
        return;
      }

      // Filter out invalid messages before processing
      const validMessages = newMsgs.filter(msg => 
        msg && 
        (msg.messageId || msg.id) && 
        msg.owner && 
        msg.text
      );

      if (validMessages.length === 0) {
        console.warn('[CommunicationChat] No valid messages to add for channel:', channelId);
        return;
      }

      setMessages(prev => {
        const merged = [...(prev[channelId] || []), ...validMessages]
          .reduce((acc: any[], m: any) => {
            const msgId = m.messageId || m.id;
            const exists = acc.find(x => (x.messageId || x.id) === msgId)
            if (!exists) acc.push(m)
            return acc
          }, [])
          .sort((a, b) => (a.order || 0) - (b.order || 0))

        // Limit messages per channel to prevent memory leaks (keep last 1000 messages)
        const limitedMessages = merged.slice(-1000);

        return { ...prev, [channelId]: limitedMessages }
      })

      // Auto-scroll on new messages if viewing this channel
      if (selectedChannel && selectedChannel.channelId === channelId) {
        setTimeout(() => containerRef.current?.scrollTo({ top: containerRef.current.scrollHeight, behavior: 'smooth' }), 50)
      }
    },
    onUnread: (info) => {
      setUnreadCount(info.unreadCount || 0)
      if (info.perChannel) {
        setPerChannelUnread(info.perChannel)
      }
    },
    onPerChannelUnread: (perChannel) => {
      setPerChannelUnread(perChannel)
    },
    onError: (err) => {
      console.error('Chat polling error', err)
      toast({ title: 'Problema de conexão', description: 'Tentando reconectar ao chat...' })
    }
  }), [selectedChannel])

  // Separate effect for initial channel loading (one-time)
  useEffect(() => {
    let mounted = true
    const loadChannels = async () => {
      try {
        setIsLoadingChannels(true)
        setShowWhatsAppLoader(true)

        const res = await CommunicationApi.listChannels({ sortBy: 'lastMessageAt', sortOrder: 'desc' })

        if (!mounted) return

        if (res.status === 200) {
          const list = res.data?.data?.channels || []
          setChannels(list)

          // If there's a target channel ID from URL, try to select it
          if (targetChannelId) {
            const targetChannel = list.find(ch => ch.channelId === targetChannelId)
            if (targetChannel) {
              setSelectedChannel(targetChannel)
              console.log(`[CommunicationChat] Auto-selected channel from URL: ${targetChannel.name}`)
            } else {
              console.warn(`[CommunicationChat] Target channel ${targetChannelId} not found in user's channels`)
              // Still select first channel as fallback
              if (list[0]) setSelectedChannel(list[0])
            }
          } else {
            // Default behavior: select first channel
            if (list[0]) setSelectedChannel(list[0])
          }
        } else {
          toast({ title: 'Erro', description: 'Não foi possível carregar seus canais.' })
        }
      } catch (e) {
        console.error('[CommunicationChat] Error loading channels:', e)
        toast({ title: 'Erro', description: 'Não foi possível carregar seus canais.' })
      } finally {
        if (mounted) {
          setIsLoadingChannels(false)
          setShowWhatsAppLoader(false)
        }
      }
    }

    loadChannels()

    return () => {
      mounted = false
    }
  }, [targetChannelId]) // Only depend on targetChannelId, not polling

  // Separate effect for starting unread polling (continuous)
  useEffect(() => {
    // Start only unread polling, not message polling
    polling.startUnreadOnly()

    return () => {
      polling.stop()
    }
  }, [polling])

  useEffect(() => {
    // Ensure selectedChannel exists and has a valid ID before polling
    if (!selectedChannel?.channelId || selectedChannel.channelId === 'undefined') {
      console.warn('[CommunicationChat] Invalid selectedChannel for polling:', selectedChannel);
      return;
    }

    // start focused polling for this channel only
    polling.pollChannel(selectedChannel.channelId, true)
    return () => {
      // Only stop if we have a valid channel ID
      if (selectedChannel?.channelId && selectedChannel.channelId !== 'undefined') {
        polling.stopChannel(selectedChannel.channelId)
      }
    }
  }, [selectedChannel, polling])

  const sendText = async () => {
    // Enhanced validation for selectedChannel and input
    if (!selectedChannel?.channelId || selectedChannel.channelId === 'undefined' || !input.trim() || sending) {
      console.warn('[CommunicationChat] Cannot send message - invalid state:', {
        selectedChannel: selectedChannel?.channelId,
        inputLength: input.trim().length,
        sending
      });
      return;
    }

    setSending(true)
    const chId = selectedChannel.channelId
    const trimmedText = input.trim()

    // optimistic message
    const temp: Message = {
      messageId: `temp-${Date.now()}`,
      channelId: chId,
      order: (messages[chId]?.[messages[chId].length - 1]?.order || 0) + 0.0001,
      messageType: 'text',
      text: trimmedText,
      owner: { userId: authData?._id || 'me', name: authData?.name || 'Você' },
      createdAt: new Date().toISOString(),
    }
    setMessages(prev => ({ ...prev, [chId]: [...(prev[chId] || []), temp] }))
    setInput('')

    try {
      // Ensure we send the trimmed text that matches backend validation requirements
      const res = await CommunicationApi.sendMessageJson({
        channelId: chId,
        messageType: 'text',
        text: trimmedText
      })

      const real = res.data

      console.log('real message received:', real)
      
      // Validate that we received a valid message from API
      if (!real || !real.messageId) {
        console.error('[CommunicationChat] Invalid message received from API:', real);
        toast({ title: 'Erro', description: 'Resposta inválida do servidor.' });
        // Remove the optimistic message since we can't replace it with real one
        setMessages(prev => ({
          ...prev,
          [chId]: (prev[chId] || []).filter(m => (m.messageId || m.id) !== temp.messageId)
        }))
        return;
      }
      
      setMessages(prev => ({
        ...prev,
        [chId]: (prev[chId] || []).map(m => (m.messageId || m.id) === temp.messageId ? real as any : m)
      }))
      // advance read cursor since user sees their own message
      polling.advanceReadCursor(chId)

      // Clear unread count for this channel since user is actively participating
      setPerChannelUnread(prev => ({
        ...prev,
        [chId]: 0
      }))
    } catch (e) {
      console.error('Error sending message:', e)
      toast({ title: 'Falha ao enviar', description: 'Sua mensagem não foi enviada. Tentaremos novamente.' })
      // Remove the optimistic message on error
      setMessages(prev => ({
        ...prev,
        [chId]: (prev[chId] || []).filter(m => (m.messageId || m.id) !== temp.messageId)
      }))
    } finally {
      setSending(false)
    }
  }

  const onChannelClick = async (ch: Channel) => {
    // Validate channel before setting as selected
    if (!ch?.channelId || ch.channelId === 'undefined') {
      console.error('[CommunicationChat] Invalid channel provided to onChannelClick:', ch);
      toast({ title: 'Erro', description: 'Canal inválido selecionado.' });
      return;
    }

    setSelectedChannel(ch)
    // ensure initial fetch
    const existing = messages[ch.channelId]?.length || 0
    if (!existing) {
      setIsLoadingMessages(true)
      try {
        await polling.pollChannel(ch.channelId, true)
      } catch (error) {
        console.error('[CommunicationChat] Error polling channel on selection:', error);
        toast({ title: 'Erro', description: 'Não foi possível carregar mensagens do canal.' });
      } finally {
        setIsLoadingMessages(false)
      }
    }

    // mark viewed and clear unread count for this channel
    try {
      await polling.advanceReadCursor(ch.channelId)
    } catch (error) {
      console.error('[CommunicationChat] Error advancing read cursor:', error);
    }

    // Optimistically clear unread count for this channel
    setPerChannelUnread(prev => ({
      ...prev,
      [ch.channelId]: 0
    }))
  }

  // Show WhatsApp loader while loading channels
  if (showWhatsAppLoader) {
    return <WhatsAppLoader isVisible={true} />
  }

  // If teacherView we keep same layout (teacher header already present) -> reuse same container.
  return (
    <div className={`h-[calc(100vh-4rem)] grid grid-cols-12 gap-6 p-6 bg-gradient-to-br from-[#040A2F] via-[#0A1B4D] to-[#0F2057] text-blue-100 ${teacherView ? 'teacher-chat' : ''}`}>
      <div className="col-span-12 md:col-span-4 lg:col-span-3 bg-gradient-to-b from-blue-800/25 to-blue-900/25 rounded-2xl border border-blue-600/20 backdrop-blur-xl shadow-2xl overflow-hidden">
        <div className="px-6 py-4 border-b border-blue-600/20 flex items-center justify-between bg-gradient-to-r from-blue-700/20 to-blue-600/20">
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <div className="font-semibold text-lg">Conversas</div>
          </div>
          {unreadCount > 0 && (
            <Badge className="bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg border-0 px-3 py-1">
              {unreadCount}
            </Badge>
          )}
        </div>
        <div className="max-h-[calc(100%-5rem)] overflow-y-auto custom-scrollbar">
          {isLoadingChannels ? (
            // Loading skeleton for channels
            <div className="space-y-2 p-2">
              {[1, 2, 3, 4, 5].map(i => (
                <div key={i} className="px-4 py-4 animate-pulse rounded-xl bg-blue-800/10">
                  <div className="flex items-center gap-4">
                    <div className="h-12 w-12 bg-blue-700/30 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-blue-700/30 rounded-lg w-3/4 mb-2"></div>
                      <div className="h-3 bg-blue-700/20 rounded-lg w-1/2"></div>
                    </div>
                  </div>
                </div>
              ))}
              <div className="text-center text-blue-400 text-sm mt-4 opacity-75">
                Carregando conversas...
              </div>
            </div>
          ) : (
            <div className="p-2 space-y-1">
              {channels.map(ch => {
                const channelUnreadCount = perChannelUnread[ch.channelId] || 0;
                const isSelected = selectedChannel?.channelId === ch.channelId;
                return (
                  <motion.button
                    key={ch.channelId}
                    onClick={() => onChannelClick(ch)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className={`w-full text-left px-4 py-4 rounded-xl transition-all duration-200 group relative overflow-hidden ${
                      isSelected
                        ? 'bg-gradient-to-r from-blue-600/40 to-blue-700/40 shadow-lg border border-blue-500/30'
                        : 'hover:bg-blue-800/20 hover:shadow-md'
                    }`}
                  >
                    {/* Selection indicator */}
                    {isSelected && (
                      <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-400 to-blue-600 rounded-r-full"></div>
                    )}

                    <div className="flex items-center gap-4">
                      <div className="relative">
                        <Avatar className="h-12 w-12 ring-2 ring-blue-500/20 transition-all group-hover:ring-blue-400/40">
                          <AvatarImage src={ch.members[0]?.avatarUrl} />
                          <AvatarFallback className="bg-gradient-to-br from-blue-500 to-blue-600 text-white font-semibold">
                            {ch.name?.[0]?.toUpperCase()||'C'}
                          </AvatarFallback>
                        </Avatar>
                        {channelUnreadCount > 0 && (
                          <Badge className="absolute -top-1 -right-1 h-6 w-6 flex items-center justify-center p-0 text-xs bg-gradient-to-r from-red-500 to-red-600 text-white border-2 border-blue-900 shadow-lg animate-pulse">
                            {channelUnreadCount > 99 ? '99+' : channelUnreadCount}
                          </Badge>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-semibold text-blue-100 truncate group-hover:text-white transition-colors">
                            {ch.name}
                          </span>
                          {channelUnreadCount > 0 && selectedChannel?.channelId !== ch.channelId && (
                            <Badge className="bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs px-2 py-1 shadow-md">
                              {channelUnreadCount}
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2 text-xs text-blue-300">
                            <span className="px-2 py-1 bg-blue-700/30 rounded-full">
                              {ch.type==='GROUP'?'👥 Grupo':'💬 Chat'}
                            </span>
                            {ch.members && ch.members.length > 0 && (
                              <PresenceList
                                users={ch.members.map(m => ({
                                  id: m.id,
                                  status: m.presence || 'offline',
                                  name: m.name
                                }))}
                                maxVisible={2}
                              />
                            )}
                          </div>
                          {ch.lastMessageAt && (
                            <span className="text-xs text-blue-400 opacity-75">
                              {new Date(ch.lastMessageAt).toLocaleTimeString('pt-BR', {
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.button>
                )
              })}
              {channels.length===0 && !isLoadingChannels && (
                <div className="p-8 text-center">
                  <div className="text-4xl mb-4 opacity-50">💬</div>
                  <div className="text-sm text-blue-300 opacity-75">Nenhuma conversa encontrada</div>
                  <div className="text-xs text-blue-400 opacity-50 mt-1">Suas conversas aparecerão aqui</div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      <div className="col-span-12 md:col-span-8 lg:col-span-9 bg-gradient-to-b from-blue-800/25 to-blue-900/25 rounded-2xl border border-blue-600/20 backdrop-blur-xl shadow-2xl overflow-hidden flex flex-col">
        <div className="px-6 py-4 border-b border-blue-600/20 flex items-center justify-between bg-gradient-to-r from-blue-700/20 to-blue-600/20">
          <div className="flex items-center gap-4">
            {selectedChannel && (
              <Avatar className="h-10 w-10 ring-2 ring-blue-400/30">
                <AvatarImage src={selectedChannel.members[0]?.avatarUrl} />
                <AvatarFallback className="bg-gradient-to-br from-blue-500 to-blue-600 text-white font-semibold">
                  {selectedChannel.name?.[0]?.toUpperCase()||'C'}
                </AvatarFallback>
              </Avatar>
            )}
            <div>
              <div className="font-semibold text-lg text-blue-100">
                {selectedChannel?.name || 'Selecione uma conversa'}
              </div>
              {selectedChannel && (
                <div className="text-xs text-blue-300 flex items-center gap-2">
                  <span>{selectedChannel.members?.length || 0} membros</span>
                  <span>•</span>
                  <span className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    Online
                  </span>
                </div>
              )}
            </div>
          </div>
          {selectedChannel && (
            <div className="flex items-center gap-3">
              <Button variant="ghost" size="sm" className="text-blue-300 hover:text-blue-100 hover:bg-blue-700/30">
                <span className="text-lg">⋮</span>
              </Button>
            </div>
          )}
        </div>
        <div className="flex-1 overflow-y-auto" ref={containerRef}>
          <div className="p-4 space-y-3">
            {isLoadingMessages ? (
              // Message loading skeleton
              <div className="space-y-4">
                {[1, 2, 3, 4, 5].map(i => (
                  <div key={i} className={`flex ${i % 2 === 0 ? 'justify-end' : 'justify-start'}`}>
                    <div className="animate-pulse">
                      <div className={`max-w-[85%] p-3 rounded-2xl ${i % 2 === 0 ? 'bg-blue-700/30' : 'bg-blue-800/30'}`}>
                        <div className="space-y-2">
                          <div className="h-4 bg-blue-600/30 rounded w-3/4"></div>
                          {i % 3 === 0 && <div className="h-4 bg-blue-600/30 rounded w-1/2"></div>}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : !selectedChannel ? (
              // Empty state when no channel is selected
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-blue-300">
                  <div className="text-6xl mb-4">💬</div>
                  <h3 className="text-lg font-medium mb-2">Bem-vindo ao Chat</h3>
                  <p className="text-sm opacity-75">Selecione uma conversa para começar</p>
                </div>
              </div>
            ) : (
              <AnimatePresence initial={false}>
                {(() => {
                  const channelId = selectedChannel?.channelId;
                  if (!channelId) return [];
                  const channelMessages = messages[channelId] || [];
                  return channelMessages
                    // Filter out invalid messages to prevent crashes
                    .filter(m => m && (m.messageId || m.id) && m.owner && m.text)
                    .map((m, index) => {
                    const isMe = m.owner.userId === authData?._id; // Use real user ID from auth context
                    const isLastMessage = index === channelMessages.length - 1;
                  return (
                    <motion.div
                      key={m.messageId || m.id}
                      initial={{ opacity: 0, y: 20, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.95 }}
                      transition={{ duration: 0.3, ease: "easeOut" }}
                      className={`flex ${isMe ? 'justify-end' : 'justify-start'} group`}
                    >
                      <div className={`max-w-[85%] relative ${isMe ? 'ml-12' : 'mr-12'}`}>
                        {!isMe && (
                          <div className="flex items-center gap-2 mb-1 ml-1">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={m.owner.avatarUrl} />
                              <AvatarFallback className="text-xs bg-blue-600 text-white">
                                {m.owner.name?.[0]?.toUpperCase() || 'U'}
                              </AvatarFallback>
                            </Avatar>
                            <span className="text-xs text-blue-300 font-medium">{m.owner.name}</span>
                          </div>
                        )}
                        <div className={`p-4 rounded-2xl shadow-lg backdrop-blur-sm transition-all duration-200 group-hover:shadow-xl ${
                          isMe
                            ? 'bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-tr-md'
                            : 'bg-gradient-to-br from-blue-800/60 to-blue-900/60 text-blue-100 rounded-tl-md border border-blue-600/30'
                        }`}>
                          {(m.messageType === 'text' || m.type === 'text') && (
                            <div className="prose prose-sm max-w-none prose-invert">
                              <ReactMarkdown>{sanitizeMessage(m.text || '')}</ReactMarkdown>
                            </div>
                          )}
                          {m.attachments?.map(att => (
                            <div key={att.id} className="mt-3">
                              {att.mimeType.startsWith('image/') ? (
                                // eslint-disable-next-line @next/next/no-img-element
                                <img
                                  src={att.url}
                                  alt={att.name}
                                  className="rounded-xl border border-blue-500/30 max-w-full h-auto shadow-md hover:shadow-lg transition-shadow"
                                />
                              ) : (
                                <a
                                  href={att.url}
                                  target="_blank"
                                  rel="noreferrer"
                                  className="inline-flex items-center gap-2 px-3 py-2 bg-blue-700/30 rounded-lg hover:bg-blue-600/40 transition-colors text-blue-200 hover:text-white"
                                >
                                  <span>📎</span>
                                  {att.name}
                                </a>
                              )}
                            </div>
                          ))}
                          <div className={`text-xs mt-2 opacity-70 ${isMe ? 'text-blue-100' : 'text-blue-300'}`}>
                            {new Date(m.createdAt).toLocaleTimeString('pt-BR', {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                            {isMe && isLastMessage && (
                              <span className="ml-2">✓</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )
                  });
                })()}
                {(() => {
                  const channelId = selectedChannel?.channelId;
                  if (!channelId) return null;
                  const channelMessages = messages[channelId] || [];
                  return channelMessages.length === 0 && !isLoadingMessages && (
                  <div className="flex items-center justify-center h-32">
                    <div className="text-center text-blue-300">
                      <div className="text-4xl mb-2">📝</div>
                      <p className="text-sm opacity-75">Nenhuma mensagem ainda</p>
                      <p className="text-xs opacity-50 mt-1">Seja o primeiro a enviar uma mensagem!</p>
                    </div>
                  </div>
                  );
                })()}
              </AnimatePresence>
            )}
          </div>
        </div>
        <div className="p-6 border-t border-blue-600/20 bg-gradient-to-r from-blue-800/10 to-blue-700/10">
          <div className="flex items-end gap-3">
            <div className="flex items-center gap-2">
              <label className="cursor-pointer">
                <input type="file" accept="image/*,application/pdf" className="hidden" onChange={async (e)=>{
                  const file = e.target.files?.[0];
                  if (!file || !selectedChannel?.channelId || selectedChannel.channelId === 'undefined') {
                    console.warn('[CommunicationChat] Invalid file upload state:', { file: !!file, selectedChannel: selectedChannel?.channelId });
                    return;
                  }

                  // Validate file size (10MB limit)
                  const maxSize = 10 * 1024 * 1024; // 10MB
                  if (file.size > maxSize) {
                    toast({ title: 'Arquivo muito grande', description: 'O arquivo deve ter no máximo 10MB.' });
                    return;
                  }

                  setUploading(true);
                  try {
                    // Use existing uploader service
                    const { default: FileController } = await import('@/services/FILE_UPLOADER/api/FileController');
                    const uploadRes = await FileController.uploadFile(file);

                    if (!uploadRes?.data?.url) {
                      throw new Error('Upload failed - no URL returned');
                    }

                    const form = new FormData();
                    form.append('channelId', selectedChannel.channelId);
                    form.append('messageType', file.type.startsWith('image/') ? 'image' : 'file');
                    form.append('fileUrl', uploadRes.data.url);
                    form.append('fileName', file.name);
                    form.append('mimeType', file.type);

                    await CommunicationApi.sendMessageMultipart(form);
                    toast({ title: 'Sucesso', description: 'Arquivo enviado com sucesso!' });
                  } catch (err) {
                    console.error('[CommunicationChat] File upload error:', err);
                    toast({
                      title: 'Falha no upload',
                      description: err instanceof Error ? err.message : 'Não foi possível enviar o arquivo.'
                    });
                  } finally {
                    setUploading(false);
                    // Reset file input
                    e.target.value = '';
                  }
                }} />
                <Button
                  asChild
                  variant="ghost"
                  size="sm"
                  className="text-blue-300 hover:text-blue-100 hover:bg-blue-700/30 rounded-full p-2"
                  title="Anexar arquivo"
                  disabled={uploading}
                >
                  <span>
                    {uploading ? (
                      <div className="w-5 h-5 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <ImageIcon size={20} />
                    )}
                  </span>
                </Button>
              </label>
              <Button
                variant="ghost"
                size="sm"
                className="text-blue-300 hover:text-blue-100 hover:bg-blue-700/30 rounded-full p-2"
                title="Adicionar emoji"
              >
                <SmilePlus size={20} />
              </Button>
            </div>

            <div className="flex-1 relative">
              <Input
                placeholder="Digite sua mensagem..."
                value={input}
                onChange={(e) => {
                  setInput(e.target.value)
                }}
                onKeyDown={(e)=>{
                  if(e.key==='Enter') sendText()
                }}
                className="bg-blue-900/40 border-blue-600/30 text-blue-100 placeholder:text-blue-400 rounded-2xl px-4 py-3 pr-12 focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all"
              />
              <Button
                onClick={sendText}
                disabled={!input.trim() || sending}
                className="absolute right-2 top-1/2 -translate-y-1/2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-full p-2 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                size="sm"
              >
                {sending ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Send size={16} />
                )}
              </Button>
            </div>
          </div>

          {/* Typing indicator placeholder */}
          <div className="mt-2 h-4 flex items-center">
            <div className="text-xs text-blue-400 opacity-75">
              {/* This could show "User is typing..." */}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}